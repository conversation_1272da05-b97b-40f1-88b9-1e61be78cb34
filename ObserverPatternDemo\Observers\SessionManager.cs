using ObserverPatternDemo.Interfaces;
using ObserverPatternDemo.Models;

namespace ObserverPatternDemo.Observers
{
    /// <summary>
    /// 会话管理器
    /// 职责：管理用户会话生命周期
    /// </summary>
    public class SessionManager : IObserver
    {
        /// <summary>
        /// 观察者名称
        /// </summary>
        public string Name => "会话管理器";

        /// <summary>
        /// 异步处理用户登录事件
        /// </summary>
        /// <param name="loginEvent">用户登录事件</param>
        /// <returns>异步任务</returns>
        public async Task UpdateAsync(UserLoginEvent loginEvent)
        {
            Console.WriteLine($"[{Name}] 开始管理用户 '{loginEvent.Username}' 的会话...");

            // 模拟创建新的用户会话
            await Task.Delay(120);
            var session = await CreateNewSessionAsync(loginEvent);
            Console.WriteLine($"[{Name}] 新会话已创建 - 会话ID: {session.SessionId}, 过期时间: {session.ExpiryTime:yyyy-MM-dd HH:mm:ss}");

            // 模拟设置会话过期时间
            await Task.Delay(80);
            await SetSessionExpiryAsync(session.SessionId, TimeSpan.FromHours(8));
            Console.WriteLine($"[{Name}] 会话过期时间已设置为8小时");

            // 模拟清理过期会话
            await Task.Delay(150);
            var cleanedCount = await CleanExpiredSessionsAsync(loginEvent.UserId);
            Console.WriteLine($"[{Name}] 已清理 {cleanedCount} 个过期会话");

            // 模拟同步会话信息到集群
            await Task.Delay(100);
            await SyncSessionToClusterAsync(session);
            Console.WriteLine($"[{Name}] 会话信息已同步到集群节点");

            // 模拟更新会话活跃状态
            await Task.Delay(90);
            await UpdateSessionActivityAsync(session.SessionId);
            Console.WriteLine($"[{Name}] 会话活跃状态已更新");

            // 模拟设置会话安全策略
            await Task.Delay(70);
            await ApplySecurityPolicyAsync(session, loginEvent);
            Console.WriteLine($"[{Name}] 会话安全策略已应用");

            Console.WriteLine($"[{Name}] 会话管理处理完成");
        }

        /// <summary>
        /// 创建新的用户会话
        /// </summary>
        /// <param name="loginEvent">登录事件</param>
        /// <returns>会话信息</returns>
        private async Task<SessionInfo> CreateNewSessionAsync(UserLoginEvent loginEvent)
        {
            await Task.Delay(50);
            
            var session = new SessionInfo
            {
                SessionId = loginEvent.SessionId,
                UserId = loginEvent.UserId,
                Username = loginEvent.Username,
                CreatedTime = loginEvent.LoginTime,
                ExpiryTime = loginEvent.LoginTime.AddHours(8),
                IpAddress = loginEvent.IpAddress,
                DeviceInfo = loginEvent.DeviceInfo,
                IsActive = true
            };

            Console.WriteLine($"[{Name}] 会话数据已存储到Redis - Key: session:{session.SessionId}");
            
            return session;
        }

        /// <summary>
        /// 设置会话过期时间
        /// </summary>
        /// <param name="sessionId">会话ID</param>
        /// <param name="expiry">过期时间间隔</param>
        /// <returns>异步任务</returns>
        private async Task SetSessionExpiryAsync(string sessionId, TimeSpan expiry)
        {
            await Task.Delay(30);
            Console.WriteLine($"[{Name}] Redis TTL已设置 - Key: session:{sessionId}, TTL: {expiry.TotalSeconds}秒");
        }

        /// <summary>
        /// 清理过期会话
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>清理的会话数量</returns>
        private async Task<int> CleanExpiredSessionsAsync(string userId)
        {
            await Task.Delay(80);
            
            // 模拟查找用户的所有会话
            var userSessions = await GetUserSessionsAsync(userId);
            var expiredSessions = userSessions.Where(s => s.ExpiryTime < DateTime.Now).ToList();
            
            // 模拟删除过期会话
            foreach (var session in expiredSessions)
            {
                await DeleteSessionAsync(session.SessionId);
            }
            
            if (expiredSessions.Any())
            {
                Console.WriteLine($"[{Name}] 清理过期会话: {string.Join(", ", expiredSessions.Select(s => s.SessionId.Substring(0, 8) + "..."))}");
            }
            
            return expiredSessions.Count;
        }

        /// <summary>
        /// 同步会话信息到集群
        /// </summary>
        /// <param name="session">会话信息</param>
        /// <returns>异步任务</returns>
        private async Task SyncSessionToClusterAsync(SessionInfo session)
        {
            await Task.Delay(60);
            
            // 模拟同步到多个集群节点
            var clusterNodes = new[] { "Node-1", "Node-2", "Node-3" };
            
            foreach (var node in clusterNodes)
            {
                await Task.Delay(20);
                Console.WriteLine($"[{Name}] 会话已同步到 {node}");
            }
        }

        /// <summary>
        /// 更新会话活跃状态
        /// </summary>
        /// <param name="sessionId">会话ID</param>
        /// <returns>异步任务</returns>
        private async Task UpdateSessionActivityAsync(string sessionId)
        {
            await Task.Delay(40);
            Console.WriteLine($"[{Name}] 会话最后活跃时间已更新 - 会话: {sessionId.Substring(0, 8)}...");
        }

        /// <summary>
        /// 应用会话安全策略
        /// </summary>
        /// <param name="session">会话信息</param>
        /// <param name="loginEvent">登录事件</param>
        /// <returns>异步任务</returns>
        private async Task ApplySecurityPolicyAsync(SessionInfo session, UserLoginEvent loginEvent)
        {
            await Task.Delay(50);
            
            // 模拟应用安全策略
            var policies = new List<string>();
            
            // IP地址限制
            if (!loginEvent.IpAddress.StartsWith("192.168."))
            {
                policies.Add("外网IP访问限制");
            }
            
            // 设备绑定
            policies.Add("设备指纹验证");
            
            // 并发会话限制
            policies.Add("单用户最大会话数限制");
            
            Console.WriteLine($"[{Name}] 安全策略已应用: {string.Join(", ", policies)}");
        }

        /// <summary>
        /// 获取用户所有会话
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>会话列表</returns>
        private async Task<List<SessionInfo>> GetUserSessionsAsync(string userId)
        {
            await Task.Delay(40);
            
            // 模拟返回用户会话列表
            var random = new Random();
            var sessionCount = random.Next(1, 4);
            var sessions = new List<SessionInfo>();
            
            for (int i = 0; i < sessionCount; i++)
            {
                sessions.Add(new SessionInfo
                {
                    SessionId = Guid.NewGuid().ToString(),
                    UserId = userId,
                    CreatedTime = DateTime.Now.AddHours(-random.Next(1, 24)),
                    ExpiryTime = DateTime.Now.AddHours(random.Next(-2, 8)),
                    IsActive = random.Next(0, 2) == 1
                });
            }
            
            return sessions;
        }

        /// <summary>
        /// 删除会话
        /// </summary>
        /// <param name="sessionId">会话ID</param>
        /// <returns>异步任务</returns>
        private async Task DeleteSessionAsync(string sessionId)
        {
            await Task.Delay(20);
            Console.WriteLine($"[{Name}] 会话已删除 - 会话: {sessionId.Substring(0, 8)}...");
        }
    }

    /// <summary>
    /// 会话信息模型
    /// </summary>
    public class SessionInfo
    {
        public string SessionId { get; set; } = string.Empty;
        public string UserId { get; set; } = string.Empty;
        public string Username { get; set; } = string.Empty;
        public DateTime CreatedTime { get; set; }
        public DateTime ExpiryTime { get; set; }
        public string IpAddress { get; set; } = string.Empty;
        public string DeviceInfo { get; set; } = string.Empty;
        public bool IsActive { get; set; }
    }
}
