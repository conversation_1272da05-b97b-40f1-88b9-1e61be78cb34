using ObserverPatternDemo.Interfaces;
using ObserverPatternDemo.Models;

namespace ObserverPatternDemo.Observers
{
    /// <summary>
    /// 安全日志记录器
    /// 职责：记录用户登录的安全审计信息
    /// </summary>
    public class SecurityLogger : IObserver
    {
        /// <summary>
        /// 观察者名称
        /// </summary>
        public string Name => "安全日志记录器";

        /// <summary>
        /// 异步处理用户登录事件
        /// </summary>
        /// <param name="loginEvent">用户登录事件</param>
        /// <returns>异步任务</returns>
        public async Task UpdateAsync(UserLoginEvent loginEvent)
        {
            Console.WriteLine($"[{Name}] 开始处理用户 '{loginEvent.Username}' 的安全审计...");

            // 模拟异步处理：记录登录时间、IP地址、设备信息
            await Task.Delay(200); // 模拟数据库写入延迟
            Console.WriteLine($"[{Name}] 记录登录信息 - 时间: {loginEvent.LoginTime:yyyy-MM-dd HH:mm:ss}, IP: {loginEvent.IpAddress}, 设备: {loginEvent.DeviceInfo}");

            // 模拟检测异常登录行为
            await Task.Delay(150);
            if (IsAbnormalLogin(loginEvent))
            {
                Console.WriteLine($"[{Name}] ⚠️ 检测到异常登录行为 - 用户: {loginEvent.Username}, IP: {loginEvent.IpAddress}");
                await SendSecurityAlertAsync(loginEvent);
            }
            else
            {
                Console.WriteLine($"[{Name}] ✅ 登录行为正常");
            }

            // 模拟写入安全审计数据库
            await Task.Delay(100);
            Console.WriteLine($"[{Name}] 安全审计记录已写入数据库，会话ID: {loginEvent.SessionId}");

            // 模拟生成安全报告
            await Task.Delay(80);
            Console.WriteLine($"[{Name}] 安全报告已更新");

            Console.WriteLine($"[{Name}] 安全审计处理完成");
        }

        /// <summary>
        /// 检测是否为异常登录
        /// </summary>
        /// <param name="loginEvent">登录事件</param>
        /// <returns>是否异常</returns>
        private bool IsAbnormalLogin(UserLoginEvent loginEvent)
        {
            // 模拟异常检测逻辑：简单的IP地址检查
            return loginEvent.IpAddress.StartsWith("192.168.") == false;
        }

        /// <summary>
        /// 发送安全告警
        /// </summary>
        /// <param name="loginEvent">登录事件</param>
        /// <returns>异步任务</returns>
        private async Task SendSecurityAlertAsync(UserLoginEvent loginEvent)
        {
            await Task.Delay(50);
            Console.WriteLine($"[{Name}] 🚨 安全告警已发送给管理员 - 用户: {loginEvent.Username}");
        }
    }
}
