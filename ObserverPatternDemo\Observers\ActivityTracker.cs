using ObserverPatternDemo.Interfaces;
using ObserverPatternDemo.Models;

namespace ObserverPatternDemo.Observers
{
    /// <summary>
    /// 活动跟踪器
    /// 职责：记录和分析用户行为数据
    /// </summary>
    public class ActivityTracker : IObserver
    {
        /// <summary>
        /// 观察者名称
        /// </summary>
        public string Name => "活动跟踪器";

        /// <summary>
        /// 异步处理用户登录事件
        /// </summary>
        /// <param name="loginEvent">用户登录事件</param>
        /// <returns>异步任务</returns>
        public async Task UpdateAsync(UserLoginEvent loginEvent)
        {
            Console.WriteLine($"[{Name}] 开始跟踪用户 '{loginEvent.Username}' 的活动数据...");

            // 模拟记录用户登录行为
            await Task.Delay(100);
            await RecordLoginBehaviorAsync(loginEvent);
            Console.WriteLine($"[{Name}] 登录行为已记录 - 用户: {loginEvent.Username}, 时间: {loginEvent.LoginTime:HH:mm:ss}");

            // 模拟更新用户活跃度统计
            await Task.Delay(150);
            var activityScore = await UpdateUserActivityScoreAsync(loginEvent.UserId);
            Console.WriteLine($"[{Name}] 用户活跃度已更新 - 当前活跃度评分: {activityScore}");

            // 模拟触发个性化推荐算法
            await Task.Delay(200);
            await TriggerRecommendationAlgorithmAsync(loginEvent);
            Console.WriteLine($"[{Name}] 个性化推荐算法已触发，正在计算推荐内容...");

            // 模拟更新用户画像数据
            await Task.Delay(180);
            await UpdateUserProfileAsync(loginEvent);
            Console.WriteLine($"[{Name}] 用户画像数据已更新");

            // 模拟行为模式分析
            await Task.Delay(120);
            var behaviorPattern = await AnalyzeBehaviorPatternAsync(loginEvent.UserId);
            Console.WriteLine($"[{Name}] 行为模式分析完成 - 模式类型: {behaviorPattern}");

            Console.WriteLine($"[{Name}] 活动跟踪处理完成");
        }

        /// <summary>
        /// 记录用户登录行为
        /// </summary>
        /// <param name="loginEvent">登录事件</param>
        /// <returns>异步任务</returns>
        private async Task RecordLoginBehaviorAsync(UserLoginEvent loginEvent)
        {
            await Task.Delay(50);
            
            // 模拟记录登录行为数据
            var behaviorData = new
            {
                UserId = loginEvent.UserId,
                Action = "Login",
                Timestamp = loginEvent.LoginTime,
                IpAddress = loginEvent.IpAddress,
                Device = loginEvent.DeviceInfo,
                SessionId = loginEvent.SessionId
            };

            Console.WriteLine($"[{Name}] 行为数据已存储到数据仓库");
        }

        /// <summary>
        /// 更新用户活跃度评分
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>活跃度评分</returns>
        private async Task<double> UpdateUserActivityScoreAsync(string userId)
        {
            await Task.Delay(80);
            
            // 模拟计算活跃度评分
            var random = new Random();
            var baseScore = random.NextDouble() * 100;
            var loginBonus = 5.0; // 登录奖励分数
            
            var newScore = Math.Min(100.0, baseScore + loginBonus);
            
            Console.WriteLine($"[{Name}] 活跃度计算 - 基础分: {baseScore:F1}, 登录奖励: {loginBonus}, 总分: {newScore:F1}");
            
            return newScore;
        }

        /// <summary>
        /// 触发个性化推荐算法
        /// </summary>
        /// <param name="loginEvent">登录事件</param>
        /// <returns>异步任务</returns>
        private async Task TriggerRecommendationAlgorithmAsync(UserLoginEvent loginEvent)
        {
            await Task.Delay(100);
            
            // 模拟获取用户历史行为
            var userHistory = await GetUserHistoryAsync(loginEvent.UserId);
            
            // 模拟推荐算法计算
            await Task.Delay(80);
            var recommendations = await CalculateRecommendationsAsync(userHistory);
            
            Console.WriteLine($"[{Name}] 推荐算法计算完成 - 生成 {recommendations.Count} 个推荐项目");
        }

        /// <summary>
        /// 更新用户画像数据
        /// </summary>
        /// <param name="loginEvent">登录事件</param>
        /// <returns>异步任务</returns>
        private async Task UpdateUserProfileAsync(UserLoginEvent loginEvent)
        {
            await Task.Delay(90);
            
            // 模拟更新用户画像
            var profileUpdates = new
            {
                LastLoginTime = loginEvent.LoginTime,
                LoginFrequency = "高频用户",
                PreferredDevice = GetDeviceType(loginEvent.DeviceInfo),
                ActiveTimeSlot = GetTimeSlot(loginEvent.LoginTime),
                LocationPattern = GetLocationPattern(loginEvent.IpAddress)
            };
            
            Console.WriteLine($"[{Name}] 用户画像更新 - 设备偏好: {profileUpdates.PreferredDevice}, 活跃时段: {profileUpdates.ActiveTimeSlot}");
        }

        /// <summary>
        /// 分析用户行为模式
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>行为模式类型</returns>
        private async Task<string> AnalyzeBehaviorPatternAsync(string userId)
        {
            await Task.Delay(60);
            
            // 模拟行为模式分析
            var patterns = new[] { "规律型", "随机型", "工作日型", "周末型", "夜猫子型" };
            var random = new Random();
            
            return patterns[random.Next(patterns.Length)];
        }

        /// <summary>
        /// 获取用户历史行为数据
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>历史行为数据</returns>
        private async Task<List<string>> GetUserHistoryAsync(string userId)
        {
            await Task.Delay(40);
            return new List<string> { "浏览商品", "搜索内容", "点击推荐", "分享内容" };
        }

        /// <summary>
        /// 计算个性化推荐
        /// </summary>
        /// <param name="userHistory">用户历史</param>
        /// <returns>推荐列表</returns>
        private async Task<List<string>> CalculateRecommendationsAsync(List<string> userHistory)
        {
            await Task.Delay(60);
            return new List<string> { "推荐商品A", "推荐内容B", "推荐活动C" };
        }

        /// <summary>
        /// 获取设备类型
        /// </summary>
        /// <param name="deviceInfo">设备信息</param>
        /// <returns>设备类型</returns>
        private string GetDeviceType(string deviceInfo)
        {
            if (deviceInfo.Contains("Mobile")) return "移动端";
            if (deviceInfo.Contains("Desktop")) return "桌面端";
            return "未知设备";
        }

        /// <summary>
        /// 获取时间段
        /// </summary>
        /// <param name="loginTime">登录时间</param>
        /// <returns>时间段</returns>
        private string GetTimeSlot(DateTime loginTime)
        {
            var hour = loginTime.Hour;
            return hour switch
            {
                >= 6 and < 12 => "上午",
                >= 12 and < 18 => "下午",
                >= 18 and < 24 => "晚上",
                _ => "深夜"
            };
        }

        /// <summary>
        /// 获取位置模式
        /// </summary>
        /// <param name="ipAddress">IP地址</param>
        /// <returns>位置模式</returns>
        private string GetLocationPattern(string ipAddress)
        {
            if (ipAddress.StartsWith("192.168.")) return "内网";
            return "外网";
        }
    }
}
