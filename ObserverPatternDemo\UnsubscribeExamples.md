# 观察者模式取消订阅实现方式

本文档展示了三种不同的观察者取消订阅实现方式，每种方式都有其适用场景和优缺点。

## 方式一：析构函数（Finalizer）

### 实现示例
```csharp
public class SecurityLogger : IObserver
{
    private readonly ISubject _subject;
    
    public SecurityLogger(ISubject subject)
    {
        _subject = subject ?? throw new ArgumentNullException(nameof(subject));
        _subject.Subscribe(this);
    }
    
    // 析构函数：自动取消订阅，防止内存泄漏
    ~SecurityLogger()
    {
        _subject?.Unsubscribe(this);
    }
}
```

### 优点
- **自动化**：无需手动调用，垃圾回收时自动执行
- **简单**：实现简单，代码量少
- **防止遗忘**：开发者无需记住手动清理

### 缺点
- **不确定性**：析构函数执行时机不确定
- **性能影响**：对象会被放入终结队列，延迟回收
- **资源延迟释放**：可能导致资源长时间占用

### 适用场景
- 简单的观察者模式实现
- 对资源释放时机要求不严格的场景
- 作为其他方式的备用方案

## 方式二：IDisposable接口（推荐）

### 实现示例
```csharp
public class OnlineStatusUpdater : IObserver, IDisposable
{
    private readonly ISubject _subject;
    private bool _disposed = false;
    
    public OnlineStatusUpdater(ISubject subject)
    {
        _subject = subject ?? throw new ArgumentNullException(nameof(subject));
        _subject.Subscribe(this);
    }
    
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }
    
    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                // 释放托管资源
                _subject?.Unsubscribe(this);
            }
            _disposed = true;
        }
    }
    
    // 析构函数：作为Dispose的备用方案
    ~OnlineStatusUpdater()
    {
        Dispose(false);
    }
}
```

### 优点
- **确定性**：可以精确控制资源释放时机
- **标准模式**：符合.NET的资源管理最佳实践
- **性能优化**：通过GC.SuppressFinalize避免终结队列
- **双重保险**：结合析构函数提供备用清理机制

### 缺点
- **需要手动调用**：开发者必须记住调用Dispose
- **代码复杂**：实现相对复杂
- **容易遗忘**：如果忘记调用Dispose，仍可能泄漏

### 适用场景
- **生产环境推荐**：最适合生产环境使用
- 需要精确控制资源释放时机的场景
- 长期运行的应用程序
- 资源敏感的应用

### 使用示例
```csharp
// 使用using语句自动释放
using var observer = new OnlineStatusUpdater(subject);

// 或手动释放
var observer = new OnlineStatusUpdater(subject);
try
{
    // 使用observer
}
finally
{
    observer.Dispose();
}
```

## 方式三：WeakReference（高级方案）

### 实现示例
```csharp
public class WelcomeMessageSender : IObserver
{
    private readonly WeakReference<ISubject> _subjectRef;
    
    public WelcomeMessageSender(ISubject subject)
    {
        if (subject == null) throw new ArgumentNullException(nameof(subject));
        
        _subjectRef = new WeakReference<ISubject>(subject);
        subject.Subscribe(this);
    }
    
    ~WelcomeMessageSender()
    {
        if (_subjectRef.TryGetTarget(out var subject))
        {
            subject.Unsubscribe(this);
        }
    }
}
```

### 优点
- **避免循环引用**：不会阻止Subject被垃圾回收
- **自动清理**：Subject被回收后自动断开连接
- **内存友好**：减少内存泄漏风险

### 缺点
- **复杂性**：实现和理解相对复杂
- **调试困难**：弱引用可能使调试变得困难
- **不确定性**：Subject可能在意外时机被回收

### 适用场景
- 存在循环引用风险的场景
- Subject生命周期不确定的情况
- 内存敏感的应用程序
- 高级开发者使用

## 被观察者端的改进

为了支持真正的取消订阅，需要改进UserLoginManager：

```csharp
public class UserLoginManager : ISubject
{
    private readonly List<IObserver> _observers;
    private readonly object _lockObject = new object();
    
    public void Subscribe(IObserver observer)
    {
        lock (_lockObject)
        {
            if (!_observers.Contains(observer))
            {
                _observers.Add(observer);
            }
        }
    }
    
    public void Unsubscribe(IObserver observer)
    {
        lock (_lockObject)
        {
            _observers.Remove(observer);
        }
    }
}
```

## 最佳实践建议

1. **生产环境推荐**：使用IDisposable模式（方式二）
2. **简单场景**：可以使用析构函数（方式一）
3. **复杂场景**：考虑WeakReference（方式三）
4. **组合使用**：可以结合多种方式提供多重保障
5. **测试验证**：确保取消订阅功能正常工作
6. **文档说明**：在代码中明确说明资源管理策略

## 运行结果

程序运行时会显示取消订阅的效果：
```
=== 演示取消订阅功能 ===

演示手动释放观察者资源...
[UserLoginManager] 观察者 '在线状态更新器' 已取消订阅
观察者资源已释放
触发垃圾回收...
垃圾回收完成
```

这表明取消订阅功能正常工作，观察者成功从被观察者中移除。
