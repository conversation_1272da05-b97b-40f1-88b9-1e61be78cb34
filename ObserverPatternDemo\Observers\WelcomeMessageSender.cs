using ObserverPatternDemo.Interfaces;
using ObserverPatternDemo.Models;

namespace ObserverPatternDemo.Observers
{
    /// <summary>
    /// 欢迎消息发送器
    /// 职责：向用户推送个性化欢迎信息
    /// </summary>
    public class WelcomeMessageSender : IObserver
    {
        /// <summary>
        /// 观察者名称
        /// </summary>
        public string Name => "欢迎消息发送器";

        /// <summary>
        /// 异步处理用户登录事件
        /// </summary>
        /// <param name="loginEvent">用户登录事件</param>
        /// <returns>异步任务</returns>
        public async Task UpdateAsync(UserLoginEvent loginEvent)
        {
            Console.WriteLine($"[{Name}] 开始为用户 '{loginEvent.Username}' 准备欢迎消息...");

            // 模拟获取用户未读消息数量
            var unreadCount = await GetUnreadMessageCountAsync(loginEvent.UserId);
            await Task.Delay(150);
            Console.WriteLine($"[{Name}] 获取未读消息数量: {unreadCount} 条");

            // 模拟生成个性化欢迎内容
            var welcomeMessage = await GeneratePersonalizedWelcomeAsync(loginEvent);
            await Task.Delay(200);
            Console.WriteLine($"[{Name}] 生成个性化欢迎消息: \"{welcomeMessage}\"");

            // 模拟推送系统通知和公告
            await Task.Delay(120);
            var announcements = await GetSystemAnnouncementsAsync();
            Console.WriteLine($"[{Name}] 推送系统公告: {announcements.Count} 条新公告");

            // 模拟发送实时消息到客户端
            await Task.Delay(100);
            await SendRealTimeMessageAsync(loginEvent.SessionId, welcomeMessage, unreadCount);
            Console.WriteLine($"[{Name}] 实时消息已发送到客户端，会话ID: {loginEvent.SessionId}");

            // 模拟发送推送通知
            await Task.Delay(80);
            Console.WriteLine($"[{Name}] 推送通知已发送到用户设备: {loginEvent.DeviceInfo}");

            Console.WriteLine($"[{Name}] 欢迎消息发送完成");
        }

        /// <summary>
        /// 获取用户未读消息数量
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>未读消息数量</returns>
        private async Task<int> GetUnreadMessageCountAsync(string userId)
        {
            await Task.Delay(50);
            var random = new Random();
            return random.Next(0, 20);
        }

        /// <summary>
        /// 生成个性化欢迎消息
        /// </summary>
        /// <param name="loginEvent">登录事件</param>
        /// <returns>欢迎消息</returns>
        private async Task<string> GeneratePersonalizedWelcomeAsync(UserLoginEvent loginEvent)
        {
            await Task.Delay(100);
            
            var timeOfDay = GetTimeOfDayGreeting(loginEvent.LoginTime);
            var lastLoginInfo = await GetLastLoginInfoAsync(loginEvent.UserId);
            
            return $"{timeOfDay}，{loginEvent.Username}！欢迎回来，{lastLoginInfo}";
        }

        /// <summary>
        /// 获取时间段问候语
        /// </summary>
        /// <param name="loginTime">登录时间</param>
        /// <returns>问候语</returns>
        private string GetTimeOfDayGreeting(DateTime loginTime)
        {
            var hour = loginTime.Hour;
            return hour switch
            {
                >= 5 and < 12 => "早上好",
                >= 12 and < 18 => "下午好",
                >= 18 and < 23 => "晚上好",
                _ => "夜深了"
            };
        }

        /// <summary>
        /// 获取上次登录信息
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>上次登录信息</returns>
        private async Task<string> GetLastLoginInfoAsync(string userId)
        {
            await Task.Delay(30);
            var random = new Random();
            var daysAgo = random.Next(1, 7);
            return $"您上次登录是{daysAgo}天前";
        }

        /// <summary>
        /// 获取系统公告
        /// </summary>
        /// <returns>公告列表</returns>
        private async Task<List<string>> GetSystemAnnouncementsAsync()
        {
            await Task.Delay(40);
            return new List<string>
            {
                "系统将于本周末进行维护升级",
                "新功能上线：智能推荐系统",
                "安全提醒：请定期更换密码"
            };
        }

        /// <summary>
        /// 发送实时消息到客户端
        /// </summary>
        /// <param name="sessionId">会话ID</param>
        /// <param name="message">消息内容</param>
        /// <param name="unreadCount">未读消息数</param>
        /// <returns>异步任务</returns>
        private async Task SendRealTimeMessageAsync(string sessionId, string message, int unreadCount)
        {
            await Task.Delay(50);
            Console.WriteLine($"[{Name}] WebSocket消息已发送 - 会话: {sessionId}, 未读数: {unreadCount}");
        }
    }
}
