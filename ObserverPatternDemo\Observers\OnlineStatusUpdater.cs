using ObserverPatternDemo.Interfaces;
using ObserverPatternDemo.Models;

namespace ObserverPatternDemo.Observers
{
    /// <summary>
    /// 在线状态更新器
    /// 职责：更新用户在系统中的在线状态显示
    /// </summary>
    public class OnlineStatusUpdater : IObserver
    {
        /// <summary>
        /// 观察者名称
        /// </summary>
        public string Name => "在线状态更新器";

        /// <summary>
        /// 异步处理用户登录事件
        /// </summary>
        /// <param name="loginEvent">用户登录事件</param>
        /// <returns>异步任务</returns>
        public async Task UpdateAsync(UserLoginEvent loginEvent)
        {
            Console.WriteLine($"[{Name}] 开始更新用户 '{loginEvent.Username}' 的在线状态...");

            // 模拟更新用户在好友列表中的状态
            await Task.Delay(120);
            Console.WriteLine($"[{Name}] 更新好友列表状态 - 用户: {loginEvent.Username}, 状态: {loginEvent.PreviousStatus} → {loginEvent.CurrentStatus}");

            // 模拟同步状态到分布式缓存
            await Task.Delay(180);
            Console.WriteLine($"[{Name}] 状态已同步到Redis缓存 - Key: user_status:{loginEvent.UserId}");

            // 模拟通知相关用户的好友
            var friendsList = await GetUserFriendsAsync(loginEvent.UserId);
            await Task.Delay(100);
            Console.WriteLine($"[{Name}] 已通知 {friendsList.Count} 个好友用户状态变更");

            // 模拟更新在线用户统计
            await Task.Delay(90);
            var onlineCount = await UpdateOnlineUserCountAsync();
            Console.WriteLine($"[{Name}] 在线用户统计已更新，当前在线用户数: {onlineCount}");

            // 模拟更新用户最后活跃时间
            await Task.Delay(60);
            Console.WriteLine($"[{Name}] 用户最后活跃时间已更新: {loginEvent.LoginTime:yyyy-MM-dd HH:mm:ss}");

            Console.WriteLine($"[{Name}] 在线状态更新完成");
        }

        /// <summary>
        /// 获取用户好友列表
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <returns>好友列表</returns>
        private async Task<List<string>> GetUserFriendsAsync(string userId)
        {
            await Task.Delay(50);
            // 模拟返回好友列表
            return new List<string> { "friend1", "friend2", "friend3", "friend4", "friend5" };
        }

        /// <summary>
        /// 更新在线用户数量统计
        /// </summary>
        /// <returns>当前在线用户数</returns>
        private async Task<int> UpdateOnlineUserCountAsync()
        {
            await Task.Delay(30);
            // 模拟返回在线用户数
            var random = new Random();
            return random.Next(1000, 5000);
        }
    }
}
